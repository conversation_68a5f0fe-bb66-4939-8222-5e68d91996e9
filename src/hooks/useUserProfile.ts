import { useState, useEffect } from 'react';
import { UserProfile, WeatherData } from '@/types/user';
import { UserProfileService } from '@/services/database';
import PersistentAuthService from '@/services/persistentAuth';
import { getWeatherData } from '@/services/api';

interface UseUserProfileReturn {
  userProfile: UserProfile | null;
  weatherData: WeatherData | null;
  isLoadingProfile: boolean;
  isLoadingWeather: boolean;
  error: string | null;
  refreshWeather: () => Promise<void>;
  refreshProfile: () => Promise<void>;
}

export function useUserProfile(): UseUserProfileReturn {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [isLoadingWeather, setIsLoadingWeather] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load user profile from database
  const loadUserProfile = async () => {
    try {
      setIsLoadingProfile(true);
      setError(null);

      // First, get the current authenticated user to ensure we have the correct user ID
      const currentUser = await PersistentAuthService.getCurrentUser();
      if (!currentUser) {
        console.warn('No authenticated user found');
        setUserProfile(null);
        return;
      }

      // Always refresh the user profile from the backend to ensure we get the correct profile
      // for the currently authenticated user. This prevents issues with stale localStorage data.
      console.log('Refreshing user profile from backend for user:', currentUser.id);
      const profile = await PersistentAuthService.refreshUserProfile();

      setUserProfile(profile);

      // If profile exists and has location data, load weather
      if (profile && profile.latitude && profile.longitude) {
        await loadWeatherData(profile.latitude, profile.longitude);
      }
    } catch (err) {
      console.error('Error loading user profile:', err);
      setError('Failed to load user profile');
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Load weather data for given coordinates
  const loadWeatherData = async (lat: number, lon: number) => {
    if (import.meta.env.VITE_ENABLE_WEATHER_INTEGRATION !== 'true') {
      return;
    }

    try {
      setIsLoadingWeather(true);
      setError(null);
      
      const weather = await getWeatherData(lat, lon);
      setWeatherData(weather);
    } catch (err) {
      console.warn('Weather data failed to load (non-critical):', err);
      // Don't set error for weather failures as it's non-critical
      setWeatherData(null);
    } finally {
      setIsLoadingWeather(false);
    }
  };

  // Refresh weather data
  const refreshWeather = async () => {
    if (userProfile && userProfile.latitude && userProfile.longitude) {
      await loadWeatherData(userProfile.latitude, userProfile.longitude);
    }
  };

  // Refresh user profile
  const refreshProfile = async () => {
    try {
      setIsLoadingProfile(true);
      setError(null);

      // Try to refresh from backend first
      let profile = await PersistentAuthService.refreshUserProfile();

      // If backend refresh fails, try to get from localStorage
      if (!profile) {
        profile = PersistentAuthService.getCurrentUserProfile();
      }

      setUserProfile(profile);

      // If profile exists and has location data, load weather
      if (profile && profile.latitude && profile.longitude) {
        await loadWeatherData(profile.latitude, profile.longitude);
      }
    } catch (err) {
      console.error('Error refreshing user profile:', err);
      setError('Failed to refresh user profile');
    } finally {
      setIsLoadingProfile(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    loadUserProfile();
  }, []);

  // Auto-refresh weather every 10 minutes
  useEffect(() => {
    if (!userProfile || !userProfile.latitude || !userProfile.longitude) {
      return;
    }

    const interval = setInterval(() => {
      loadWeatherData(userProfile.latitude, userProfile.longitude);
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(interval);
  }, [userProfile]);

  return {
    userProfile,
    weatherData,
    isLoadingProfile,
    isLoadingWeather,
    error,
    refreshWeather,
    refreshProfile,
  };
}
