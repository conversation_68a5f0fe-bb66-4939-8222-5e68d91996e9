@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* ZARA-inspired neutral palette */
    --background: 0 0% 98%;
    --foreground: 210 15% 15%;

    --card: 0 0% 100%;
    --card-foreground: 210 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 15% 15%;

    --primary: 210 15% 15%;
    --primary-foreground: 0 0% 98%;

    --secondary: 220 10% 92%;
    --secondary-foreground: 210 15% 15%;

    --muted: 220 10% 95%;
    --muted-foreground: 210 12% 45%;

    --accent: 25 20% 85%;
    --accent-foreground: 210 15% 15%;

    --destructive: 0 65% 55%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 210 15% 15%;

    /* Enhanced Zara Color Palette */
    --zara-white: 0 0% 100%;
    --zara-black: 0 0% 0%;
    --zara-charcoal: 210 15% 15%;
    --zara-light-gray: 220 10% 95%;
    --zara-medium-gray: 220 13% 91%;
    --zara-dark-gray: 210 12% 45%;

    /* iOS 26 Liquid Glass effects */
    --glass-bg: 0 0% 100% / 0.08;
    --glass-border: 0 0% 100% / 0.15;
    --glass-shadow: 0 0% 0% / 0.05;

    /* Clear Glass Gradients */
    --gradient-glass: linear-gradient(135deg,
      hsl(0 0% 100% / 0.1),
      hsl(0 0% 100% / 0.02)
    );
    --gradient-shimmer: linear-gradient(110deg,
      transparent 25%,
      hsl(0 0% 100% / 0.2) 50%,
      transparent 75%
    );

    /* Advanced Liquid Glass System */
    --glass-bg-rgb: 255 255 255;
    --glass-border-rgb: 255 255 255;
    --glass-shadow-rgb: 0 0 0;

    /* Multiple glass opacity levels */
    --glass-subtle: 0.08;
    --glass-light: 0.12;
    --glass-medium: 0.16;
    --glass-strong: 0.25;
    --glass-prominent: 0.4;
    --glass-navigation: 0.7;
    --glass-modal: 0.8;

    /* Multiple blur intensities */
    --blur-xs: 4px;
    --blur-sm: 8px;
    --blur-md: 16px;
    --blur-lg: 24px;
    --blur-xl: 32px;
    --blur-2xl: 40px;

    /* Glass reflection and refraction */
    --glass-reflection: rgba(255, 255, 255, 0.3);
    --glass-refraction: rgba(255, 255, 255, 0.1);
    --glass-highlight: rgba(255, 255, 255, 0.5);

    /* Premium animation timing */
    --timing-instant: 100ms;
    --timing-fast: 150ms;
    --timing-normal: 300ms;
    --timing-slow: 500ms;
    --timing-extra-slow: 800ms;
    --timing-luxurious: 1200ms;

    /* Premium easing curves */
    --ease-glass: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-premium: cubic-bezier(0.23, 1, 0.32, 1);
    --ease-liquid: cubic-bezier(0.16, 1, 0.3, 1);

    /* Zara-inspired spacing system */
    --space-xs: 8px;
    --space-sm: 16px;
    --space-md: 24px;
    --space-lg: 32px;
    --space-xl: 48px;
    --space-2xl: 64px;
    --space-3xl: 96px;

    --radius: 1rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 0 0% 12%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 97%;
    --sidebar-accent-foreground: 0 0% 12%;
    --sidebar-border: 0 0% 90%;
    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
}

@layer components {
  /* Enhanced Zara Typography System */
  .zara-hero {
    font-size: 2rem;
    font-weight: 100;
    letter-spacing: 0.05em;
    line-height: 1.1;
    color: hsl(var(--zara-charcoal));
  }

  .zara-title {
    font-size: 1.5rem;
    font-weight: 200;
    letter-spacing: 0.03em;
    line-height: 1.2;
    color: hsl(var(--zara-charcoal));
  }

  .zara-subtitle {
    font-size: 1.125rem;
    font-weight: 300;
    letter-spacing: 0.02em;
    line-height: 1.3;
    color: hsl(var(--zara-dark-gray));
  }

  .zara-body {
    font-size: 0.875rem;
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.5;
    color: hsl(var(--zara-charcoal));
  }

  .zara-caption {
    font-size: 0.75rem;
    font-weight: 300;
    letter-spacing: 0.02em;
    line-height: 1.4;
    color: hsl(var(--zara-dark-gray));
    text-transform: uppercase;
  }

  /* iOS 26 Liquid Glass Panel */
  .glass-panel {
    @apply border border-white/15;
    background: linear-gradient(135deg,
      hsl(0 0% 100% / 0.1),
      hsl(0 0% 100% / 0.02)
    );
    box-shadow:
      0 8px 32px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(var(--glass-border)),
      0 0 0 1px hsl(0 0% 100% / 0.05);
  }

  /* Advanced Liquid Glass Effect Utilities */
  .glass-subtle {
    backdrop-filter: blur(var(--blur-sm));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-subtle)),
      hsl(0 0% 100% / 0.02)
    );
    border: 1px solid hsl(0 0% 100% / 0.15);
    box-shadow:
      0 4px 16px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.15);
  }

  .glass-light {
    backdrop-filter: blur(var(--blur-md));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-light)),
      hsl(0 0% 100% / 0.04)
    );
    border: 1px solid hsl(0 0% 100% / 0.2);
    box-shadow:
      0 6px 20px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.2);
  }

  .glass-medium {
    backdrop-filter: blur(var(--blur-md));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-medium)),
      hsl(0 0% 100% / 0.06)
    );
    border: 1px solid hsl(0 0% 100% / 0.25);
    box-shadow:
      0 8px 32px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.25),
      inset 0 -1px 0 hsl(0 0% 100% / 0.1);
  }

  .glass-strong {
    backdrop-filter: blur(var(--blur-lg));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-strong)),
      hsl(0 0% 100% / 0.1)
    );
    border: 1px solid hsl(0 0% 100% / 0.3);
    box-shadow:
      0 12px 40px hsla(var(--glass-shadow)),
      inset 0 2px 0 hsl(0 0% 100% / 0.3),
      inset 0 -1px 0 hsl(0 0% 100% / 0.15);
  }

  .glass-prominent {
    backdrop-filter: blur(var(--blur-xl));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-prominent)),
      hsl(0 0% 100% / 0.15)
    );
    border: 1px solid hsl(0 0% 100% / 0.4);
    box-shadow:
      0 16px 48px hsla(var(--glass-shadow)),
      inset 0 2px 0 hsl(0 0% 100% / 0.4),
      inset 0 -2px 0 hsl(0 0% 100% / 0.2);
  }

  .glass-navigation {
    backdrop-filter: blur(var(--blur-xl));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-navigation)),
      hsl(0 0% 100% / 0.3)
    );
    border-top: 1px solid hsl(0 0% 100% / 0.4);
    box-shadow:
      0 -8px 32px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.4);
  }

  .glass-modal {
    backdrop-filter: blur(var(--blur-2xl));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-modal)),
      hsl(0 0% 100% / 0.4)
    );
    border: 1px solid hsl(0 0% 100% / 0.5);
    box-shadow:
      0 24px 64px hsla(var(--glass-shadow)),
      inset 0 2px 0 hsl(0 0% 100% / 0.5),
      inset 0 -2px 0 hsl(0 0% 100% / 0.25);
  }

  .glass-hero {
    backdrop-filter: blur(var(--blur-lg));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-medium)),
      hsl(0 0% 100% / 0.06)
    );
    border: 1px solid hsl(0 0% 100% / 0.25);
    box-shadow:
      0 20px 56px hsla(var(--glass-shadow)),
      inset 0 2px 0 hsl(0 0% 100% / 0.25),
      inset 0 -1px 0 hsl(0 0% 100% / 0.1);
  }

  .glass-product {
    backdrop-filter: blur(var(--blur-sm));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-subtle)),
      hsl(0 0% 100% / 0.02)
    );
    border: 1px solid hsl(0 0% 100% / 0.15);
    box-shadow:
      0 2px 8px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.15);
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .glass-product:hover {
    backdrop-filter: blur(var(--blur-md));
    background: linear-gradient(135deg,
      hsl(0 0% 100% / var(--glass-light)),
      hsl(0 0% 100% / 0.04)
    );
    box-shadow:
      0 8px 24px hsla(var(--glass-shadow)),
      inset 0 1px 0 hsl(0 0% 100% / 0.2);
    transform: translateY(-2px);
  }

  /* iOS 26 Liquid Glass Panel */
  .glass-panel {
    @apply border border-white/15;
    background: linear-gradient(135deg,
      hsl(0 0% 100% / 0.1),
      hsl(0 0% 100% / 0.02)
    );
    box-shadow:
      0 8px 32px hsla(var(--glass-shadow) / 0.05),
      inset 0 1px 0 hsl(0 0% 100% / 0.15),
      0 0 0 1px hsl(0 0% 100% / 0.05);
  }

  /* Liquid Glass Button */
  .glass-button {
    @apply glass-panel px-8 py-4 rounded-full font-medium text-white transition-all duration-300;
    @apply hover:scale-105 hover:shadow-2xl;
    background: linear-gradient(135deg,
      hsl(0 0% 100% / 0.12),
      hsl(0 0% 100% / 0.04)
    );
  }

  .glass-button:hover {
    background: linear-gradient(135deg,
      hsl(0 0% 100% / 0.18),
      hsl(0 0% 100% / 0.08)
    );
    box-shadow:
      0 12px 40px hsla(var(--glass-shadow) / 0.05),
      inset 0 1px 0 hsl(0 0% 100% / 0.15),
      0 0 0 1px hsl(0 0% 100% / 0.08);
  }

  /* Shimmer Animation */
  .shimmer {
    position: relative;
    overflow: hidden;
  }

  .shimmer::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(110deg,
      transparent 25%,
      hsl(0 0% 100% / 0.2) 50%,
      transparent 75%
    );
    animation: shimmer 2s infinite;
  }

  /* Liquid Glass Feature Card */
  .feature-card {
    @apply glass-panel p-6 rounded-2xl transition-all duration-500;
    @apply hover:scale-105 hover:-translate-y-2;
  }

  .feature-card:hover {
    background: linear-gradient(135deg,
      hsl(0 0% 100% / 0.15),
      hsl(0 0% 100% / 0.05)
    );
    box-shadow:
      0 20px 50px hsla(var(--glass-shadow) / 0.05),
      inset 0 1px 0 hsl(0 0% 100% / 0.15),
      0 0 0 1px hsl(0 0% 100% / 0.08);
  }

  /* Parallax with Refraction */
  .parallax {
    transform-style: preserve-3d;
    transition: transform 0.1s ease-out;
  }

  /* Morphing Glass Effects */
  .glass-morphing {
    transition: all var(--timing-slow) var(--ease-liquid);
  }

  .glass-morphing:hover {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-strong));
    transform: scale(1.02);
    box-shadow: 
      0 16px 48px rgba(0, 0, 0, 0.15),
      inset 0 2px 0 var(--glass-highlight),
      inset 0 -2px 0 var(--glass-refraction);
  }

  /* Premium Animation Classes */
  .animate-glass-fade-in {
    animation: glass-fade-in var(--timing-slow) var(--ease-glass);
  }

  .animate-glass-scale-in {
    animation: glass-scale-in var(--timing-normal) var(--ease-premium);
  }

  .animate-glass-slide-up {
    animation: glass-slide-up var(--timing-slow) var(--ease-liquid);
  }

  .animate-liquid-morph {
    animation: liquid-morph var(--timing-luxurious) var(--ease-liquid);
  }

  /* Smooth Transition Utilities */
  .transition-glass {
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .transition-premium {
    transition: all var(--timing-slow) var(--ease-premium);
  }

  .transition-liquid {
    transition: all var(--timing-luxurious) var(--ease-liquid);
  }

  .transition-instant {
    transition: all var(--timing-instant) var(--ease-glass);
  }

  /* Enhanced Interactive States */
  .glass-interactive {
    transition: all var(--timing-normal) var(--ease-glass);
  }

  .glass-interactive:hover {
    transform: translateY(-1px) scale(1.01);
  }

  .glass-interactive:active {
    transform: translateY(0) scale(0.99);
    transition: all var(--timing-fast) var(--ease-glass);
  }

  /* Fashion-forward image styling */
  .fashion-image {
    object-fit: cover;
    transition: all var(--timing-slow) var(--ease-premium);
    filter: contrast(1.05) saturate(0.95);
  }
  
  .fashion-image:hover {
    transform: scale(1.03);
    filter: contrast(1.1) saturate(1);
  }

  /* Enhanced Navigation States */
  .tab-bar-hidden {
    transform: translateY(100%);
    opacity: 0;
    transition: all var(--timing-normal) var(--ease-glass);
  }
  
  .tab-bar-visible {
    transform: translateY(0);
    opacity: 1;
    transition: all var(--timing-normal) var(--ease-glass);
  }

  /* Enhanced Form Styling */
  .glass-input {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
    border: 1px solid rgba(var(--glass-border), var(--glass-light));
    transition: all var(--timing-normal) var(--ease-glass);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.03),
      inset 0 1px 0 var(--glass-highlight);
  }

  .glass-input:focus {
    outline: none;
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-light));
    border-color: rgba(var(--glass-border), var(--glass-medium));
    box-shadow:
      0 8px 24px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 var(--glass-highlight),
      0 0 0 2px rgba(0, 0, 0, 0.1);
  }
}

/* Premium Keyframe Animations */
@keyframes glass-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes glass-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    transform: scale(1);
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes glass-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    backdrop-filter: blur(var(--blur-md));
  }
}

@keyframes liquid-morph {
  0% {
    backdrop-filter: blur(var(--blur-sm));
    background: rgba(var(--glass-bg), var(--glass-subtle));
  }
  50% {
    backdrop-filter: blur(var(--blur-xl));
    background: rgba(var(--glass-bg), var(--glass-strong));
    transform: scale(1.05);
  }
  100% {
    backdrop-filter: blur(var(--blur-md));
    background: rgba(var(--glass-bg), var(--glass-medium));
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Refraction Effect */
.glass-refraction {
  position: relative;
  overflow: hidden;
}

.glass-refraction::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 30% 30%, hsl(0 0% 100% / 0.1) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, hsl(0 0% 100% / 0.05) 0%, transparent 50%);
  transform: rotate(0deg) scale(1);
  transition: transform 0.3s ease-out;
  pointer-events: none;
  z-index: 1;
}

.glass-refraction::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg,
    transparent 0%,
    hsl(0 0% 100% / 0.03) 25%,
    transparent 50%,
    hsl(0 0% 100% / 0.03) 75%,
    transparent 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease-out;
  pointer-events: none;
  z-index: 2;
}

/* Scroll-triggered refraction states */
.glass-refraction.scroll-active::before {
  transform: rotate(2deg) scale(1.1);
}

.glass-refraction.scroll-active::after {
  transform: translateX(100%);
}

/* Animation keyframes */
@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes refractionPulse {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.05) rotate(1deg);
    opacity: 0.2;
  }
}

@keyframes lightSweep {
  0% { transform: translateX(-100%) skewX(-15deg); }
  100% { transform: translateX(200%) skewX(-15deg); }
}

/* Bouncy/Spring Animations for Clothing Items */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(20px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(-5px);
  }
  70% {
    transform: scale(0.95) translateY(2px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes springScale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow:
      0 0 0 0 rgba(59, 130, 246, 0.4),
      0 4px 16px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow:
      0 0 0 8px rgba(59, 130, 246, 0),
      0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

@keyframes slideInBounce {
  0% {
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
  }
  60% {
    opacity: 1;
    transform: translateX(10px) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(1deg); }
  75% { transform: rotate(-1deg); }
}

@keyframes wave {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Utility classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.8s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.6s ease-out;
}

.animate-refraction-pulse {
  animation: refractionPulse 4s ease-in-out infinite;
}

.animate-light-sweep {
  animation: lightSweep 3s ease-out infinite;
}

/* Bouncy/Spring Animation Utilities */
.animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-spring-scale {
  animation: springScale 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-slide-in-bounce {
  animation: slideInBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-wiggle {
  animation: wiggle 0.5s ease-in-out;
}

.animate-wave {
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: wave 1.5s infinite;
}

/* Enhanced Interactive States with Spring Animations */
.spring-hover {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.spring-hover:hover {
  transform: translateY(-4px) scale(1.02);
}

.spring-hover:active {
  transform: translateY(-1px) scale(0.98);
  transition: all 0.1s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Newly Added Item Highlight */
.newly-added {
  position: relative;
  overflow: hidden;
}

/* Enhanced Modal Animations */
@keyframes modal-fade-in {
  from {
    opacity: 0;
    backdrop-filter: blur(0);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

@keyframes modal-scale-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modal-scale-out {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
}

/* Dropdown Menu Animations */
@keyframes dropdown-slide-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdown-slide-out {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
}

/* Schedule Navigation Animations */
@keyframes slide-left {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slide-right {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes slide-in-from-left {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slide-in-from-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Navigation Ripple Effect */
@keyframes navigation-ripple {
  0% {
    transform: scale(0);
    opacity: 0.6;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced Animation Utility Classes */
.animate-modal-fade-in {
  animation: modal-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-modal-scale-in {
  animation: modal-scale-in 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-modal-scale-out {
  animation: modal-scale-out 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-dropdown-slide-in {
  animation: dropdown-slide-in 0.2s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-dropdown-slide-out {
  animation: dropdown-slide-out 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-left {
  animation: slide-left 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-slide-right {
  animation: slide-right 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-slide-in-from-left {
  animation: slide-in-from-left 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-slide-in-from-right {
  animation: slide-in-from-right 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.animate-navigation-ripple {
  animation: navigation-ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Navigation Zone Styles */
.navigation-zone {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation-zone::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation-zone:hover::before {
  opacity: 1;
}

.navigation-zone:active::before {
  background: rgba(0, 0, 0, 0.1);
}

/* Ripple Effect Container */
.ripple-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  pointer-events: none;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-modal-fade-in,
  .animate-modal-scale-in,
  .animate-modal-scale-out,
  .animate-dropdown-slide-in,
  .animate-dropdown-slide-out,
  .animate-slide-left,
  .animate-slide-right,
  .animate-slide-in-from-left,
  .animate-slide-in-from-right,
  .animate-navigation-ripple {
    animation: none;
  }

  .navigation-zone,
  .spring-hover,
  .glass-interactive {
    transition: none;
  }

  .navigation-zone::before {
    transition: none;
  }
}

.newly-added::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(59, 130, 246, 0.2),
    transparent
  );
  animation: lightSweep 1.5s ease-out;
  z-index: 1;
}

.newly-added::after {
  content: "";
  position: absolute;
  inset: 0;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: inherit;
  animation: pulseGlow 2s ease-in-out 3;
  z-index: 2;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur-strong {
  backdrop-filter: blur(2px);
}