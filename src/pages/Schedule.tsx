import { useState, useEffect } from 'react';
import { MoreHorizontal, Plus, X, Check, Loader2 } from 'lucide-react';
import { GlassCard } from '@/components/GlassCard';
import { GlassButton } from '@/components/ui/glass-button';
import { cn } from '@/lib/utils';
import { ClothingApiService, ClothingItem } from '@/services/clothingApi';
import { ScheduleApiService, ScheduledOutfit as ApiScheduledOutfit } from '@/services/scheduleApi';
import { OutfitApiService } from '@/services/outfitApi';
import { useUserProfile } from '@/hooks/useUserProfile';
import { ClothingItemDetailModal } from '@/components/ClothingItemDetailModal';
import { ScheduleCardMenu } from '@/components/ScheduleCardMenu';
import { ScheduleNavigation, useScheduleNavigation } from '@/components/ScheduleNavigation';

interface OutfitItem {
  id: string;
  type: 'top' | 'bottom' | 'footwear';
  imageUrl?: string;
  name: string;
  clothingItem?: ClothingItem;
}

interface DayOutfit {
  id: string;
  date: Date;
  items: OutfitItem[];
  note?: string;
}

interface ScheduledOutfit {
  [dateString: string]: DayOutfit;
}

export const Schedule = () => {
  const [selectedTab, setSelectedTab] = useState<'day' | 'month'>('day');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isLoaded, setIsLoaded] = useState(false);
  const [scheduledOutfits, setScheduledOutfits] = useState<ScheduledOutfit>({});
  const [isOutfitModalOpen, setIsOutfitModalOpen] = useState(false);
  const [selectedDateForModal, setSelectedDateForModal] = useState<Date | null>(null);
  const [isLoadingOutfits, setIsLoadingOutfits] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSavingOutfit, setIsSavingOutfit] = useState(false);
  const { userProfile, refreshProfile } = useUserProfile();

  // New state for interactive features
  const [selectedClothingItem, setSelectedClothingItem] = useState<ClothingItem | null>(null);
  const [isClothingModalOpen, setIsClothingModalOpen] = useState(false);

  // Get outfit display dates for navigation
  const outfitDisplayDates = [
    new Date(selectedDate.getTime() - 24 * 60 * 60 * 1000), // Previous day
    selectedDate, // Current day
    new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000), // Next day
  ];

  // Schedule navigation hook
  const {
    currentIndex: navIndex,
    isTransitioning,
    navigateLeft,
    navigateRight,
    canNavigateLeft,
    canNavigateRight
  } = useScheduleNavigation(outfitDisplayDates.length, 1); // Start at middle (current date)

  useEffect(() => {
    const timer = setTimeout(() => setIsLoaded(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // Load scheduled outfits when user profile is available
  useEffect(() => {
    if (userProfile?.id) {
      loadScheduledOutfits();
    }
  }, [userProfile?.id]);

  // Retry loading if user profile becomes available after initial load
  useEffect(() => {
    const timer = setTimeout(() => {
      if (userProfile?.id && Object.keys(scheduledOutfits).length === 0 && !isLoadingOutfits) {
        console.log('Retrying to load scheduled outfits after user profile became available');
        loadScheduledOutfits();
      }
    }, 1000); // Wait 1 second for user profile to load

    return () => clearTimeout(timer);
  }, [userProfile?.id, scheduledOutfits, isLoadingOutfits]);

  // Additional fallback: try to load with any available user profile after a longer delay
  useEffect(() => {
    const fallbackTimer = setTimeout(async () => {
      if (!userProfile?.id && Object.keys(scheduledOutfits).length === 0 && !isLoadingOutfits) {
        console.log('Attempting fallback user profile loading...');
        try {
          // Try to refresh the user profile
          await refreshProfile();
        } catch (error) {
          console.error('Failed to refresh user profile:', error);
        }
      }
    }, 3000); // Wait 3 seconds before fallback

    return () => clearTimeout(fallbackTimer);
  }, [userProfile?.id, scheduledOutfits, isLoadingOutfits, refreshProfile]);



  const loadScheduledOutfits = async () => {
    if (!userProfile?.id) {
      console.warn('loadScheduledOutfits: No user profile ID available');
      setError('User profile not loaded. Please refresh the page.');
      return;
    }

    setIsLoadingOutfits(true);
    setError(null);

    try {
      // Load outfits for the current month
      const currentDate = new Date();
      const apiOutfits = await ScheduleApiService.getScheduledOutfitsForMonth(
        userProfile.id,
        currentDate.getFullYear(),
        currentDate.getMonth()
      );

      // Convert API outfits to our local format
      const outfitsMap: ScheduledOutfit = {};
      apiOutfits.forEach(apiOutfit => {
        // Convert clothing items from API to our OutfitItem format
        const items: OutfitItem[] = (apiOutfit.clothing_items || []).map(clothingItem => {
          // Determine item type based on category
          let type: 'top' | 'bottom' | 'footwear' = 'top';
          const category = clothingItem.category_name?.toLowerCase() || '';

          if (
            category.includes('pant') ||
            category.includes('jean') ||
            category.includes('short') ||
            category.includes('skirt') ||
            category.includes('trouser') ||
            category.includes('bottom')
          ) {
            type = 'bottom';
          } else if (
            category.includes('shoe') ||
            category.includes('boot') ||
            category.includes('sneaker') ||
            category.includes('sandal') ||
            category.includes('footwear')
          ) {
            type = 'footwear';
          }

          return {
            id: clothingItem.id,
            type,
            name: clothingItem.name,
            imageUrl: clothingItem.image_url,
            clothingItem: {
              id: clothingItem.id,
              name: clothingItem.name,
              color: clothingItem.color,
              brand: clothingItem.brand,
              image_url: clothingItem.image_url,
              category_name: clothingItem.category_name,
              // Add other required fields with defaults
              user_id: apiOutfit.user_id,
              created_at: '',
              updated_at: ''
            }
          };
        });

        const outfit: DayOutfit = {
          id: apiOutfit.id,
          date: new Date(apiOutfit.scheduled_date),
          items,
          note: apiOutfit.notes
        };
        // Normalize the date key to match the format used in getOutfitForDate
        const dateKey = new Date(apiOutfit.scheduled_date).toISOString().split('T')[0];
        outfitsMap[dateKey] = outfit;
      });

      setScheduledOutfits(outfitsMap);

      // Log success for debugging
      console.log(`Successfully loaded ${Object.keys(outfitsMap).length} scheduled outfits for user ${userProfile.id}`);
    } catch (error) {
      console.error('Failed to load scheduled outfits:', error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('fetch')) {
          setError('Unable to connect to server. Please check your internet connection and try again.');
        } else if (error.message.includes('404')) {
          setError('No scheduled outfits found.');
        } else if (error.message.includes('500')) {
          setError('Server error occurred. Please try again later.');
        } else {
          setError(`Failed to load outfits: ${error.message}`);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
    } finally {
      setIsLoadingOutfits(false);
    }
  };

  // Generate dates for the week view
  const generateWeekDates = (centerDate: Date) => {
    const dates = [];
    const startOfWeek = new Date(centerDate);
    startOfWeek.setDate(centerDate.getDate() - 3); // 3 days before center

    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const weekDates = generateWeekDates(selectedDate);

  // Generate outfit display dates based on selected date
  const getOutfitDisplayDates = () => {
    const dates = [];
    for (let i = -1; i <= 1; i++) {
      const date = new Date(selectedDate);
      date.setDate(selectedDate.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const outfitDisplayDates = getOutfitDisplayDates();
  // Get outfit for a specific date
  const getOutfitForDate = (date: Date): DayOutfit | null => {
    const dateString = date.toISOString().split('T')[0];
    return scheduledOutfits[dateString] || null;
  };

  // Add outfit for a specific date
  const addOutfitForDate = async (date: Date, outfit: DayOutfit) => {
    if (!userProfile?.id) return;

    setIsSavingOutfit(true);
    setError(null);

    try {
      const dateString = date.toISOString().split('T')[0];

      // Extract clothing item IDs from the outfit
      const clothingItemIds = outfit.items
        .map(item => item.clothingItem?.id)
        .filter(Boolean) as string[];

      if (clothingItemIds.length === 0) {
        throw new Error('No clothing items selected for outfit');
      }

      // First, create the outfit with selected clothing items
      const createdOutfit = await OutfitApiService.createOutfit({
        userId: userProfile.id,
        name: `Outfit for ${date.toLocaleDateString()}`,
        description: outfit.note || `Outfit with ${clothingItemIds.length} items`,
        clothingItemIds,
        notes: outfit.note
      });

      // Then schedule the outfit for the selected date
      await ScheduleApiService.saveScheduledOutfit({
        userId: userProfile.id,
        outfitId: createdOutfit.id,
        scheduledDate: dateString,
        notes: outfit.note
      });

      // Update local state
      setScheduledOutfits(prev => ({
        ...prev,
        [dateString]: outfit
      }));
    } catch (error) {
      console.error('Failed to save outfit:', error);
      setError('Failed to save outfit. Please try again.');
      throw error; // Re-throw so the modal can handle it
    } finally {
      setIsSavingOutfit(false);
    }
  };

  // Handle add outfit button click
  const handleAddOutfit = (date: Date) => {
    setSelectedDateForModal(date);
    setIsOutfitModalOpen(true);
  };

  // Handle clothing item click for detail modal
  const handleClothingItemClick = (item: ClothingItem) => {
    setSelectedClothingItem(item);
    setIsClothingModalOpen(true);
  };

  // Handle schedule card menu actions
  const handleEditOutfit = (date: Date) => {
    // Open outfit modal for editing
    setSelectedDateForModal(date);
    setIsOutfitModalOpen(true);
  };

  const handleDeleteOutfit = async (date: Date) => {
    if (!userProfile?.id) return;

    const dateString = date.toISOString().split('T')[0];
    const outfit = scheduledOutfits[dateString];

    if (!outfit) return;

    // Confirm deletion
    if (!window.confirm('Are you sure you want to delete this outfit?')) {
      return;
    }

    try {
      // Delete from API
      await ScheduleApiService.deleteScheduledOutfit(userProfile.id, dateString);

      // Update local state
      setScheduledOutfits(prev => {
        const updated = { ...prev };
        delete updated[dateString];
        return updated;
      });
    } catch (error) {
      console.error('Failed to delete outfit:', error);
      setError('Failed to delete outfit. Please try again.');
    }
  };

  // Handle schedule navigation
  const handleNavigateLeft = () => {
    if (!canNavigateLeft || isTransitioning) return;

    const newDate = new Date(selectedDate.getTime() - 24 * 60 * 60 * 1000);
    setSelectedDate(newDate);
    navigateLeft();
  };

  const handleNavigateRight = () => {
    if (!canNavigateRight || isTransitioning) return;

    const newDate = new Date(selectedDate.getTime() + 24 * 60 * 60 * 1000);
    setSelectedDate(newDate);
    navigateRight();
  };

  // Helper functions
  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  const isPast = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    return compareDate < today;
  };

  const isFuture = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    return compareDate > today;
  };

  const formatDate = (date: Date) => {
    return {
      dayName: date.toLocaleDateString('en-US', { weekday: 'short' }),
      dayNumber: date.getDate()
    };
  };
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Header with Navigation Tabs */}
      <div className={cn(
        "sticky top-0 z-30 bg-white border-b border-gray-100 transition-all duration-300",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
      )}>
        <div className="px-4 sm:px-6 pt-12 sm:pt-16 pb-4 sm:pb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-center mb-4 sm:mb-6">Planner</h1>

          {/* Navigation Tabs */}
          <div className="flex justify-center mb-4 sm:mb-6">
            <div className="flex">
              <button
                onClick={() => setSelectedTab('day')}
                className={cn(
                  "px-4 sm:px-6 py-2 text-sm sm:text-base font-medium transition-all duration-200",
                  selectedTab === 'day'
                    ? 'text-black border-b-2 border-black'
                    : 'text-gray-500 hover:text-gray-700'
                )}
              >
                Day
              </button>
              <button
                onClick={() => setSelectedTab('month')}
                className={cn(
                  "px-4 sm:px-6 py-2 text-sm sm:text-base font-medium transition-all duration-200 ml-6 sm:ml-8",
                  selectedTab === 'month'
                    ? 'text-black border-b-2 border-black'
                    : 'text-gray-500 hover:text-gray-700'
                )}
              >
                Month
              </button>
            </div>
          </div>

          {/* Date Picker - Only show for Day tab */}
          {selectedTab === 'day' && (
            <div className="flex justify-center">
              {/* Mobile: 7 circular date tiles */}
              <div className="sm:hidden flex space-x-2 overflow-x-auto px-4">
                {weekDates.map((date, index) => {
                  const { dayName, dayNumber } = formatDate(date);
                  const isSelected = date.toDateString() === selectedDate.toDateString();
                  const isTodayDate = isToday(date);
                  const isPastDate = isPast(date);

                  return (
                    <button
                      key={index}
                      onClick={() => setSelectedDate(date)}
                      className={cn(
                        "flex-shrink-0 w-12 h-12 rounded-full flex flex-col items-center justify-center text-center transition-all duration-200",
                        "hover:scale-110 active:scale-95",
                        isSelected || isTodayDate
                          ? 'bg-black text-white shadow-lg'
                          : isPastDate
                          ? 'bg-gray-100 text-gray-400'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100 hover:shadow-md'
                      )}
                    >
                      <span className="text-xs font-medium">{dayName}</span>
                      <span className="text-sm font-bold">{dayNumber}</span>
                    </button>
                  );
                })}
              </div>

              {/* Tablet: 3 circular date tiles */}
              <div className="hidden sm:flex lg:hidden space-x-4">
                {weekDates.slice(2, 5).map((date, index) => {
                  const { dayName, dayNumber } = formatDate(date);
                  const isSelected = date.toDateString() === selectedDate.toDateString();
                  const isTodayDate = isToday(date);
                  const isPastDate = isPast(date);
                  const isCenter = index === 1; // Middle tile

                  return (
                    <button
                      key={index}
                      onClick={() => setSelectedDate(date)}
                      className={cn(
                        "w-16 h-16 rounded-full flex flex-col items-center justify-center text-center transition-all duration-200",
                        "hover:scale-110 hover:shadow-lg active:scale-95",
                        isSelected || isTodayDate
                          ? 'bg-black text-white shadow-lg scale-110'
                          : isPastDate
                          ? 'bg-gray-100 text-gray-400'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100',
                        isCenter && !isSelected && !isTodayDate && 'ring-2 ring-gray-200'
                      )}
                    >
                      <span className="text-xs font-medium uppercase tracking-wide">{dayName}</span>
                      <span className="text-lg font-bold">{dayNumber}</span>
                    </button>
                  );
                })}
              </div>

              {/* Desktop: 7 circular date tiles */}
              <div className="hidden lg:flex space-x-4">
                {weekDates.map((date, index) => {
                  const { dayName, dayNumber } = formatDate(date);
                  const isSelected = date.toDateString() === selectedDate.toDateString();
                  const isTodayDate = isToday(date);
                  const isPastDate = isPast(date);

                  return (
                    <button
                      key={index}
                      onClick={() => setSelectedDate(date)}
                      className={cn(
                        "w-16 h-16 rounded-full flex flex-col items-center justify-center text-center transition-all duration-200",
                        "hover:scale-110 hover:shadow-lg active:scale-95",
                        isSelected || isTodayDate
                          ? 'bg-black text-white shadow-lg scale-110'
                          : isPastDate
                          ? 'bg-gray-100 text-gray-400'
                          : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      )}
                    >
                      <span className="text-xs font-medium uppercase tracking-wide">{dayName}</span>
                      <span className="text-lg font-bold">{dayNumber}</span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
      {/* Main Content */}
      <div className={cn(
        "relative z-10 transition-all duration-500",
        isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
      )}>
        <div className="px-4 sm:px-6 pb-24 sm:pb-32">
          {selectedTab === 'day' ? (
            <>
              {/* Today Section Header */}
              <div className="text-center mb-6 sm:mb-8">
                <h2 className="text-lg sm:text-xl font-bold text-black transition-all duration-300">
                  {selectedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                  })}
                </h2>
              </div>

              {/* Error Display */}
              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                  <p className="text-red-600 text-sm font-medium text-center">{error}</p>
                  <button
                    onClick={() => setError(null)}
                    className="mt-2 text-red-500 text-xs underline block mx-auto hover:text-red-700"
                  >
                    Dismiss
                  </button>
                </div>
              )}

              {/* Outfit Cards */}
              <div className="flex justify-center">
                {isLoadingOutfits ? (
                  /* Loading State */
                  <div className="flex items-center justify-center py-12">
                    <div className="flex flex-col items-center space-y-4">
                      <Loader2 size={32} className="animate-spin text-gray-400" />
                      <p className="text-sm text-gray-500">Loading outfits...</p>
                    </div>
                  </div>
                ) : (
                  <>
                    {/* Mobile: Single card for selected date with navigation */}
                    <div className="sm:hidden w-full flex justify-center">
                      <ScheduleNavigation
                        onNavigateLeft={handleNavigateLeft}
                        onNavigateRight={handleNavigateRight}
                        disabled={isTransitioning}
                      >
                        <OutfitCard
                          date={selectedDate}
                          outfit={getOutfitForDate(selectedDate)}
                          onAddOutfit={() => handleAddOutfit(selectedDate)}
                          onClothingItemClick={handleClothingItemClick}
                          onEditOutfit={handleEditOutfit}
                          onDeleteOutfit={handleDeleteOutfit}
                          isLoading={isLoadingOutfits}
                        />
                      </ScheduleNavigation>
                    </div>

                    {/* Tablet: Single card for selected date with navigation */}
                    <div className="hidden sm:flex lg:hidden justify-center">
                      <ScheduleNavigation
                        onNavigateLeft={handleNavigateLeft}
                        onNavigateRight={handleNavigateRight}
                        disabled={isTransitioning}
                      >
                        <OutfitCard
                          date={selectedDate}
                          outfit={getOutfitForDate(selectedDate)}
                          onAddOutfit={() => handleAddOutfit(selectedDate)}
                          onClothingItemClick={handleClothingItemClick}
                          onEditOutfit={handleEditOutfit}
                          onDeleteOutfit={handleDeleteOutfit}
                          isLoading={isLoadingOutfits}
                        />
                      </ScheduleNavigation>
                    </div>

                    {/* Desktop: 3 cards with center highlighted */}
                    <div
                      key={selectedDate.toISOString()}
                      className="hidden lg:flex space-x-6 items-center"
                    >
                      {outfitDisplayDates.map((date, index) => {
                        const outfit = getOutfitForDate(date);
                        const isCenter = index === 1; // Middle card (selected date)

                        return (
                          <div
                            key={`${date.toISOString()}-${index}`}
                            className={cn(
                              "transform transition-all duration-500 ease-out",
                              isCenter
                                ? "scale-100 z-10 opacity-100"
                                : "opacity-50 scale-90"
                            )}
                            style={{
                              transitionDelay: `${index * 100}ms`
                            }}
                          >
                            {isCenter ? (
                              <ScheduleNavigation
                                onNavigateLeft={handleNavigateLeft}
                                onNavigateRight={handleNavigateRight}
                                disabled={isTransitioning}
                              >
                                <OutfitCard
                                  date={date}
                                  outfit={outfit}
                                  onAddOutfit={() => handleAddOutfit(date)}
                                  onClothingItemClick={handleClothingItemClick}
                                  onEditOutfit={handleEditOutfit}
                                  onDeleteOutfit={handleDeleteOutfit}
                                  isLoading={isLoadingOutfits}
                                />
                              </ScheduleNavigation>
                            ) : (
                              <OutfitCard
                                date={date}
                                outfit={outfit}
                                onAddOutfit={() => handleAddOutfit(date)}
                                onClothingItemClick={handleClothingItemClick}
                                onEditOutfit={handleEditOutfit}
                                onDeleteOutfit={handleDeleteOutfit}
                                isLoading={isLoadingOutfits}
                              />
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </>
                )}
              </div>
            </>
          ) : (
            /* Month view placeholder */
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-600 mb-2">Month View</h3>
              <p className="text-gray-500">Calendar integration coming soon</p>
            </div>
          )}
        </div>
      </div>

      {/* Outfit Selection Modal */}
      {isOutfitModalOpen && selectedDateForModal && (
        <OutfitSelectionModal
          isOpen={isOutfitModalOpen}
          onClose={() => {
            setIsOutfitModalOpen(false);
            setSelectedDateForModal(null);
          }}
          selectedDate={selectedDateForModal}
          onSave={async (outfit) => {
            await addOutfitForDate(selectedDateForModal, outfit);
            setIsOutfitModalOpen(false);
            setSelectedDateForModal(null);
          }}
        />
      )}

      {/* Clothing Item Detail Modal */}
      <ClothingItemDetailModal
        isOpen={isClothingModalOpen}
        onClose={() => {
          setIsClothingModalOpen(false);
          setSelectedClothingItem(null);
        }}
        item={selectedClothingItem}
      />
    </div>
  );
};

// Outfit Card Component
const OutfitCard = ({
  date,
  outfit,
  onAddOutfit,
  onClothingItemClick,
  onEditOutfit,
  onDeleteOutfit,
  isLoading = false
}: {
  date: Date;
  outfit: DayOutfit | null;
  onAddOutfit: () => void;
  onClothingItemClick?: (item: ClothingItem) => void;
  onEditOutfit?: (date: Date) => void;
  onDeleteOutfit?: (date: Date) => void;
  isLoading?: boolean;
}) => {
  const formatCardDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  // Loading state card
  if (isLoading) {
    return (
      <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6 w-64 sm:w-72 relative overflow-hidden">
        {/* Date header */}
        <div className="text-center mb-4">
          <p className="text-sm text-gray-500 font-medium">{formatCardDate(date)}</p>
        </div>

        {/* Loading content */}
        <div className="flex flex-col items-center justify-center py-12">
          <Loader2 size={32} className="animate-spin text-gray-400 mb-4" />
          <p className="text-sm text-gray-500 font-medium">Loading...</p>
        </div>

        {/* Footer placeholder */}
        <div className="mt-8 p-3 sm:p-4 bg-gradient-to-b from-gray-100 to-gray-200 rounded-xl">
          <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  // Empty state card
  if (!outfit) {
    return (
      <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl p-4 sm:p-6 w-64 sm:w-72 relative overflow-hidden transition-all duration-300 hover:scale-105">
        {/* Date header */}
        <div className="text-center mb-4">
          <p className="text-sm text-gray-500 font-medium">{formatCardDate(date)}</p>
        </div>

        {/* Empty state content */}
        <div className="flex flex-col items-center justify-center py-12">
          <button
            onClick={onAddOutfit}
            className="w-16 h-16 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 flex items-center justify-center hover:scale-110 active:scale-95 mb-4"
          >
            <Plus size={24} className="text-gray-600" />
          </button>
          <p className="text-sm text-gray-500 font-medium">Add Outfit</p>
        </div>

        {/* Footer placeholder */}
        <div className="mt-8 p-3 sm:p-4 bg-gradient-to-b from-gray-100 to-gray-200 rounded-xl">
          <p className="text-white text-xs sm:text-sm font-medium opacity-80">No outfit planned</p>
        </div>
      </div>
    );
  }

  // Outfit card with content
  return (
    <div className="bg-white rounded-2xl shadow-lg hover:shadow-xl p-4 sm:p-6 w-64 sm:w-72 relative overflow-hidden transition-all duration-300 hover:scale-105">
      {/* Date header */}
      <div className="text-center mb-4">
        <p className="text-sm text-gray-500 font-medium">{formatCardDate(date)}</p>
      </div>

      {/* Three dots menu */}
      <ScheduleCardMenu
        onEdit={() => onEditOutfit?.(date)}
        onDelete={() => onDeleteOutfit?.(date)}
        className="absolute top-3 sm:top-4 right-3 sm:right-4"
      />

      {/* Clothing items */}
      <div className="space-y-4 sm:space-y-6 mt-2 sm:mt-4">
        {/* Render actual outfit items or placeholders */}
        {['top', 'bottom', 'footwear'].map((type) => {
          const item = outfit.items.find(item => item.type === type);

          return (
            <div key={type} className="flex justify-center">
              <button
                onClick={() => item?.clothingItem && onClothingItemClick?.(item.clothingItem)}
                className="w-32 h-24 sm:w-36 sm:h-28 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center relative overflow-hidden transition-all duration-200 cursor-pointer group hover:scale-105 active:scale-95"
                disabled={!item?.clothingItem}
              >
                {item?.clothingItem?.image_url ? (
                  // Show actual clothing item image
                  <img
                    src={item.clothingItem.image_url}
                    alt={item.name}
                    className="w-full h-full object-cover rounded-xl group-hover:scale-105 transition-transform duration-200"
                  />
                ) : (
                  // Show placeholder shape based on type
                  <div className="relative group-hover:scale-105 transition-transform duration-200">
                    {type === 'top' && (
                      <>
                        <div
                          className="w-20 h-16 sm:w-24 sm:h-20 bg-gray-300 group-hover:bg-gray-400 rounded-t-lg transition-colors duration-200"
                          style={{
                            clipPath: 'polygon(25% 0%, 75% 0%, 85% 15%, 100% 15%, 100% 100%, 0% 100%, 0% 15%, 15% 15%)'
                          }}
                        />
                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-5 h-2 sm:w-6 sm:h-3 bg-gray-400 group-hover:bg-gray-500 rounded-t-full transition-colors duration-200" />
                      </>
                    )}
                    {type === 'bottom' && (
                      <div className="flex space-x-1">
                        <div className="w-8 h-16 sm:w-10 sm:h-20 bg-gray-300 group-hover:bg-gray-400 rounded-b-lg transition-colors duration-200" />
                        <div className="w-8 h-16 sm:w-10 sm:h-20 bg-gray-300 group-hover:bg-gray-400 rounded-b-lg transition-colors duration-200" />
                      </div>
                    )}
                    {type === 'footwear' && (
                      <div className="w-24 h-8 sm:w-28 sm:h-10 bg-gray-800 hover:bg-gray-900 rounded-full relative transition-all duration-200">
                        <div className="absolute top-1 left-2 w-5 h-1.5 sm:w-6 sm:h-2 bg-gray-700 group-hover:bg-gray-600 rounded-full transition-colors duration-200" />
                        <div className="absolute top-1 right-2 w-5 h-1.5 sm:w-6 sm:h-2 bg-gray-700 group-hover:bg-gray-600 rounded-full transition-colors duration-200" />
                      </div>
                    )}
                  </div>
                )}
                {/* Item name overlay */}
                {item && (
                  <div className="absolute bottom-1 left-1 right-1 bg-black bg-opacity-50 text-white text-xs p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    {item.name}
                  </div>
                )}
              </button>
            </div>
          );
        })}
      </div>

      {/* Footer with note */}
      <div className="mt-6 sm:mt-8 p-3 sm:p-4 bg-gradient-to-b from-gray-100 to-gray-200 rounded-xl hover:from-gray-200 hover:to-gray-300 transition-all duration-200 cursor-text">
        <p className="text-white text-xs sm:text-sm font-medium opacity-80 hover:opacity-100 transition-opacity duration-200">
          {outfit.note || "Add a note…"}
        </p>
      </div>
    </div>
  );
};

// Outfit Selection Modal Component
const OutfitSelectionModal = ({
  isOpen,
  onClose,
  selectedDate,
  onSave
}: {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date;
  onSave: (outfit: DayOutfit) => Promise<void>;
}) => {
  const [clothingItems, setClothingItems] = useState<ClothingItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [note, setNote] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const { userProfile } = useUserProfile();

  // Load clothing items when modal opens
  useEffect(() => {
    if (isOpen && userProfile?.id) {
      loadClothingItems();
    }
  }, [isOpen, userProfile?.id]);

  const loadClothingItems = async () => {
    if (!userProfile?.id) return;

    setIsLoading(true);
    try {
      const items = await ClothingApiService.getClothingItems(userProfile.id);
      setClothingItems(items);
    } catch (error) {
      console.error('Failed to load clothing items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleItemToggle = (itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleSave = async () => {
    if (selectedItems.size === 0) return;

    setIsSaving(true);
    try {
      // Convert selected items to outfit items
      const outfitItems: OutfitItem[] = Array.from(selectedItems).map(itemId => {
        const clothingItem = clothingItems.find(item => item.id === itemId);
        if (!clothingItem) throw new Error('Clothing item not found');

        // Determine type based on category
        let type: 'top' | 'bottom' | 'footwear' = 'top';
        const category = clothingItem.category_name?.toLowerCase() || '';

        // Bottoms: include broader terms to avoid defaulting to 'top'
        if (
          category.includes('pant') ||
          category.includes('jean') ||
          category.includes('short') ||
          category.includes('skirt') ||
          category.includes('trouser') ||
          category.includes('chino') ||
          category.includes('legging') ||
          category.includes('bottom')
        ) {
          type = 'bottom';
        } else if (
          // Footwear: include more synonyms and regional terms
          category.includes('shoe') ||
          category.includes('boot') ||
          category.includes('sneaker') ||
          category.includes('trainer') ||
          category.includes('sandal') ||
          category.includes('loafer') ||
          category.includes('heel') ||
          category.includes('flat') ||
          category.includes('flip flop') ||
          category.includes('flip-flop')
        ) {
          type = 'footwear';
        }

        return {
          id: clothingItem.id,
          type,
          name: clothingItem.name,
          imageUrl: clothingItem.image_url,
          clothingItem
        };
      });

      const outfit: DayOutfit = {
        id: `outfit-${Date.now()}`,
        date: selectedDate,
        items: outfitItems,
        note: note.trim() || undefined
      };

      await onSave(outfit);
    } catch (error) {
      console.error('Failed to save outfit:', error);
      // You might want to show an error message to the user here
    } finally {
      setIsSaving(false);
    }
  };

  const categories = ['All', ...new Set(clothingItems.map(item => item.category_name).filter(Boolean))];
  const filteredItems = selectedCategory === 'All'
    ? clothingItems
    : clothingItems.filter(item => item.category_name === selectedCategory);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Create Outfit</h2>
            <p className="text-sm text-gray-500">
              {selectedDate.toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X size={20} className="text-gray-600" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Category Filter */}
          <div className="mb-6">
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={cn(
                    "px-4 py-2 rounded-full text-sm font-medium transition-colors",
                    selectedCategory === category
                      ? "bg-black text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  )}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex items-center justify-center py-12">
              <Loader2 size={32} className="animate-spin text-gray-400" />
            </div>
          )}

          {/* Clothing Items Grid */}
          {!isLoading && (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
              {filteredItems.map(item => (
                <div
                  key={item.id}
                  onClick={() => handleItemToggle(item.id)}
                  className={cn(
                    "relative aspect-square rounded-xl overflow-hidden cursor-pointer transition-all duration-200",
                    "hover:scale-105 hover:shadow-lg",
                    selectedItems.has(item.id)
                      ? "ring-2 ring-black scale-105"
                      : "hover:ring-2 hover:ring-gray-300"
                  )}
                >
                  {item.image_url ? (
                    <img
                      src={item.image_url}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400 text-xs text-center p-2">
                        {item.name}
                      </span>
                    </div>
                  )}

                  {/* Selection indicator */}
                  {selectedItems.has(item.id) && (
                    <div className="absolute top-2 right-2 w-6 h-6 bg-black rounded-full flex items-center justify-center">
                      <Check size={14} className="text-white" />
                    </div>
                  )}

                  {/* Item info */}
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                    <p className="text-white text-xs font-medium truncate">
                      {item.name}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Note Input */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Add a note (optional)
            </label>
            <textarea
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="Add a note about this outfit..."
              className="w-full p-3 border border-gray-300 rounded-xl resize-none focus:ring-2 focus:ring-black focus:border-transparent"
              rows={3}
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            {selectedItems.size} item{selectedItems.size !== 1 ? 's' : ''} selected
          </p>
          <div className="flex space-x-3">
            <GlassButton
              variant="secondary"
              onClick={onClose}
              disabled={isSaving}
            >
              Cancel
            </GlassButton>
            <GlassButton
              variant="primary"
              onClick={handleSave}
              disabled={selectedItems.size === 0 || isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 size={16} className="animate-spin mr-2" />
                  Saving...
                </>
              ) : (
                'Save Outfit'
              )}
            </GlassButton>
          </div>
        </div>
      </div>
    </div>
  );
};
