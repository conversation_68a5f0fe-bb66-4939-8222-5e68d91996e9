import { useState, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScheduleNavigationProps {
  children: React.ReactNode;
  onNavigateLeft: () => void;
  onNavigateRight: () => void;
  className?: string;
  disabled?: boolean;
}

interface RippleEffect {
  id: number;
  x: number;
  y: number;
  side: 'left' | 'right';
}

export const ScheduleNavigation = ({
  children,
  onNavigateLeft,
  onNavigateRight,
  className,
  disabled = false
}: ScheduleNavigationProps) => {
  const [ripples, setRipples] = useState<RippleEffect[]>();
  const [isNavigating, setIsNavigating] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const rippleIdRef = useRef(0);

  const createRipple = (event: React.MouseEvent, side: 'left' | 'right') => {
    if (!containerRef.current || disabled) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const newRipple: RippleEffect = {
      id: rippleIdRef.current++,
      x,
      y,
      side
    };

    setRipples(prev => [...(prev || []), newRipple]);

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev?.filter(ripple => ripple.id !== newRipple.id) || []);
    }, 600);
  };

  const handleLeftClick = (event: React.MouseEvent) => {
    if (disabled || isNavigating) return;
    
    event.stopPropagation();
    createRipple(event, 'left');
    setIsNavigating(true);
    
    setTimeout(() => {
      onNavigateLeft();
      setIsNavigating(false);
    }, 100);
  };

  const handleRightClick = (event: React.MouseEvent) => {
    if (disabled || isNavigating) return;
    
    event.stopPropagation();
    createRipple(event, 'right');
    setIsNavigating(true);
    
    setTimeout(() => {
      onNavigateRight();
      setIsNavigating(false);
    }, 100);
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden",
        !disabled && "cursor-pointer",
        className
      )}
    >
      {/* Left Navigation Zone */}
      <div
        className={cn(
          "absolute left-0 top-0 bottom-0 w-1/3 z-10",
          "navigation-zone",
          disabled && "pointer-events-none opacity-50"
        )}
        onClick={handleLeftClick}
        role="button"
        aria-label="Previous outfit"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleLeftClick(e as any);
          }
        }}
      >
        {/* Left Navigation Indicator */}
        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center">
            <ChevronLeft size={16} className="text-gray-600" />
          </div>
        </div>
      </div>

      {/* Right Navigation Zone */}
      <div
        className={cn(
          "absolute right-0 top-0 bottom-0 w-1/3 z-10",
          "navigation-zone",
          disabled && "pointer-events-none opacity-50"
        )}
        onClick={handleRightClick}
        role="button"
        aria-label="Next outfit"
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleRightClick(e as any);
          }
        }}
      >
        {/* Right Navigation Indicator */}
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <div className="w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center">
            <ChevronRight size={16} className="text-gray-600" />
          </div>
        </div>
      </div>

      {/* Ripple Effects Container */}
      <div className="ripple-container">
        {ripples?.map((ripple) => (
          <div
            key={ripple.id}
            className={cn(
              "ripple animate-navigation-ripple",
              ripple.side === 'left' ? "bg-blue-400/30" : "bg-green-400/30"
            )}
            style={{
              left: ripple.x - 10,
              top: ripple.y - 10,
              width: 20,
              height: 20,
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative z-0 group">
        {children}
      </div>

      {/* Navigation Hints (visible on hover) */}
      <div className="absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        {/* Left hint */}
        <div className="absolute left-0 top-0 bottom-0 w-1/3 bg-gradient-to-r from-blue-500/10 to-transparent" />
        {/* Right hint */}
        <div className="absolute right-0 top-0 bottom-0 w-1/3 bg-gradient-to-l from-green-500/10 to-transparent" />
      </div>
    </div>
  );
};

// Hook for managing schedule navigation state
export const useScheduleNavigation = (totalItems: number, currentIndex: number = 0) => {
  const [currentIdx, setCurrentIdx] = useState(currentIndex);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const navigateLeft = () => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setCurrentIdx(prev => prev === 0 ? totalItems - 1 : prev - 1);
    
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const navigateRight = () => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    setCurrentIdx(prev => prev === totalItems - 1 ? 0 : prev + 1);
    
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  const navigateToIndex = (index: number) => {
    if (isTransitioning || index < 0 || index >= totalItems) return;
    
    setIsTransitioning(true);
    setCurrentIdx(index);
    
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  return {
    currentIndex: currentIdx,
    isTransitioning,
    navigateLeft,
    navigateRight,
    navigateToIndex,
    canNavigateLeft: totalItems > 1,
    canNavigateRight: totalItems > 1
  };
};
