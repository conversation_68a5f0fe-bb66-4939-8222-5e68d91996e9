import { useState, useEffect } from 'react';
import { X, Calendar, Tag, Palette, Ruler, Heart, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ClothingItem } from '@/services/clothingApi';

interface ClothingItemDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: ClothingItem | null;
}

export const ClothingItemDetailModal = ({ isOpen, onClose, item }: ClothingItemDetailModalProps) => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Re-enable body scroll when modal closes
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleClose = () => {
    setIsAnimating(false);
    // Delay actual close to allow exit animation
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);

  if (!isOpen || !item) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center p-4",
        "bg-black/60 backdrop-blur-sm",
        isAnimating ? "animate-modal-fade-in" : "animate-modal-scale-out"
      )}
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      {/* Modal Content */}
      <div
        className={cn(
          "relative w-full max-w-2xl max-h-[90vh] overflow-hidden",
          "bg-white rounded-2xl shadow-2xl",
          isAnimating ? "animate-modal-scale-in" : "animate-modal-scale-out"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-10 w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm shadow-lg hover:bg-white transition-all duration-200 flex items-center justify-center hover:scale-110 active:scale-95"
          aria-label="Close modal"
        >
          <X size={20} className="text-gray-600" />
        </button>

        {/* Scrollable Content */}
        <div className="overflow-y-auto max-h-[90vh]">
          {/* Image Section */}
          <div className="relative aspect-[4/5] bg-gray-100">
            {item.image_url ? (
              <img
                src={item.image_url}
                alt={item.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <Tag size={48} className="mx-auto mb-2" />
                  <p className="text-sm">No image available</p>
                </div>
              </div>
            )}
            
            {/* Gradient Overlay for better text readability */}
            <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/50 to-transparent" />
          </div>

          {/* Content Section */}
          <div className="p-6 space-y-6">
            {/* Header */}
            <div>
              <h2 id="modal-title" className="text-2xl font-bold text-gray-900 mb-2">
                {item.name}
              </h2>
              {item.brand && (
                <p className="text-lg text-gray-600 font-medium">{item.brand}</p>
              )}
            </div>

            {/* Details Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* Category */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                <Tag size={20} className="text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 font-medium">Category</p>
                  <p className="text-gray-900 capitalize">{item.category}</p>
                </div>
              </div>

              {/* Color */}
              {item.color && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                  <Palette size={20} className="text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Color</p>
                    <p className="text-gray-900 capitalize">{item.color}</p>
                  </div>
                </div>
              )}

              {/* Size */}
              {item.size && (
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                  <Ruler size={20} className="text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Size</p>
                    <p className="text-gray-900 uppercase">{item.size}</p>
                  </div>
                </div>
              )}

              {/* Date Added */}
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
                <Calendar size={20} className="text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500 font-medium">Added</p>
                  <p className="text-gray-900">
                    {new Date(item.created_at).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </p>
                </div>
              </div>
            </div>

            {/* Description */}
            {item.description && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Description</h3>
                <p className="text-gray-600 leading-relaxed">{item.description}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4 border-t border-gray-200">
              <button className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95">
                <Heart size={18} className="text-gray-600" />
                <span className="text-gray-700 font-medium">Favorite</span>
              </button>
              <button className="flex-1 flex items-center justify-center space-x-2 py-3 px-4 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200 hover:scale-105 active:scale-95">
                <Share2 size={18} className="text-gray-600" />
                <span className="text-gray-700 font-medium">Share</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
