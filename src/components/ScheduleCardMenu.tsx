import { useState, useRef, useEffect } from 'react';
import { MoreHorizontal, Edit3, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScheduleCardMenuProps {
  onEdit: () => void;
  onDelete: () => void;
  className?: string;
}

export const ScheduleCardMenu = ({ onEdit, onDelete, className }: ScheduleCardMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isOpen) {
      handleClose();
    } else {
      setIsOpen(true);
      setIsAnimating(true);
    }
  };

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleClose();
    onEdit();
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleClose();
    onDelete();
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);

  // Calculate menu position to prevent overflow
  const [menuPosition, setMenuPosition] = useState<'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'>('bottom-right');

  useEffect(() => {
    if (isOpen && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      // Menu dimensions (approximate)
      const menuWidth = 160;
      const menuHeight = 80;
      
      let position: typeof menuPosition = 'bottom-right';
      
      // Check horizontal overflow
      if (buttonRect.right + menuWidth > viewportWidth) {
        position = position.includes('bottom') ? 'bottom-left' : 'top-left';
      }
      
      // Check vertical overflow
      if (buttonRect.bottom + menuHeight > viewportHeight) {
        position = position.includes('right') ? 'top-right' : 'top-left';
      }
      
      setMenuPosition(position);
    }
  }, [isOpen]);

  return (
    <div className={cn("relative", className)}>
      {/* Menu Button */}
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className={cn(
          "w-7 h-7 sm:w-8 sm:h-8 rounded-full",
          "bg-gray-200 hover:bg-gray-300 transition-all duration-200",
          "flex items-center justify-center",
          "hover:scale-110 active:scale-95",
          "focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-1",
          isOpen && "bg-gray-300 scale-110"
        )}
        aria-label="More options"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <MoreHorizontal size={14} className="sm:size-4 text-gray-600" />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          ref={menuRef}
          className={cn(
            "absolute z-50 w-40 bg-white rounded-xl shadow-lg border border-gray-200",
            "py-2 overflow-hidden",
            isAnimating ? "animate-dropdown-slide-in" : "animate-dropdown-slide-out",
            // Position classes
            menuPosition === 'bottom-right' && "top-full right-0 mt-2",
            menuPosition === 'bottom-left' && "top-full left-0 mt-2",
            menuPosition === 'top-right' && "bottom-full right-0 mb-2",
            menuPosition === 'top-left' && "bottom-full left-0 mb-2"
          )}
          role="menu"
          aria-orientation="vertical"
        >
          {/* Edit Option */}
          <button
            onClick={handleEdit}
            className={cn(
              "w-full flex items-center space-x-3 px-4 py-2.5",
              "text-left text-sm text-gray-700",
              "hover:bg-gray-50 transition-colors duration-150",
              "focus:outline-none focus:bg-gray-50"
            )}
            role="menuitem"
          >
            <Edit3 size={16} className="text-gray-500" />
            <span>Edit Outfit</span>
          </button>

          {/* Delete Option */}
          <button
            onClick={handleDelete}
            className={cn(
              "w-full flex items-center space-x-3 px-4 py-2.5",
              "text-left text-sm text-red-600",
              "hover:bg-red-50 transition-colors duration-150",
              "focus:outline-none focus:bg-red-50"
            )}
            role="menuitem"
          >
            <Trash2 size={16} className="text-red-500" />
            <span>Delete Outfit</span>
          </button>
        </div>
      )}
    </div>
  );
};
